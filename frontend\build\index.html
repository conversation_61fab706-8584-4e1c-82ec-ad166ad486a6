<!doctype html><html lang="en"><head><meta charset="UTF-8"><meta name="viewport" content="width=device-width,initial-scale=1"><meta name="theme-color" content="#2563EB"><meta name="description" content="App Builder 201 - Build your application with minimal setup"><title>App Builder 201</title><link rel="icon" href="/favicon.ico" type="image/x-icon"><link rel="shortcut icon" href="/favicon.ico" type="image/x-icon"><link rel="apple-touch-icon" href="/logo192.png"><link rel="manifest" href="/manifest.json"><link rel="preload" href="/static/css/main.css" as="style"><link rel="stylesheet" href="/static/css/main.css"><style>/* Simple loading animation styles */
    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 100vh;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    }

    .loading-spinner {
      width: 50px;
      height: 50px;
      border: 5px solid #f3f3f3;
      border-top: 5px solid #2563EB;
      border-radius: 50%;
      animation: spin 1s linear infinite;
      margin-bottom: 20px;
    }</style><script defer="defer" src="/static/js/runtime.46cc9fba.js"></script><script defer="defer" src="/static/js/react.d46b8884.js"></script><script defer="defer" src="/static/js/5108.647133a7.js"></script><script defer="defer" src="/static/js/978.c7b2a90d.js"></script><script defer="defer" src="/static/js/5611.395a2adf.js"></script><script defer="defer" src="/static/js/7020.d7aff1bf.js"></script><script defer="defer" src="/static/js/5547.1f761a8d.js"></script><script defer="defer" src="/static/js/1724.6e05bd76.js"></script><script defer="defer" src="/static/js/8014.e914d075.js"></script><script defer="defer" src="/static/js/1843.705599c5.js"></script><script defer="defer" src="/static/js/1239.eed218f9.js"></script><script defer="defer" src="/static/js/921.e9eeed4e.js"></script><script defer="defer" src="/static/js/8492.51948fc4.js"></script><script defer="defer" src="/static/js/6164.759c8776.js"></script><script defer="defer" src="/static/js/3575.a31ba066.js"></script><script defer="defer" src="/static/js/3370.80a1c4d3.js"></script><script defer="defer" src="/static/js/3112.b4b1ddbe.js"></script><script defer="defer" src="/static/js/6741.dc7f3b0d.js"></script><script defer="defer" src="/static/js/1021.ec346b25.js"></script><script defer="defer" src="/static/js/7557.07d102f5.js"></script><script defer="defer" src="/static/js/8732.fe2907d6.js"></script><script defer="defer" src="/static/js/4976.cd1a9a32.js"></script><script defer="defer" src="/static/js/7767.911e6d06.js"></script><script defer="defer" src="/static/js/5588.253384ed.js"></script><script defer="defer" src="/static/js/5874.916c923f.js"></script><script defer="defer" src="/static/js/main.cc59def3.js"></script><link href="/static/css/main.708ff8dd.css" rel="stylesheet"></head><body><noscript>You need to enable JavaScript to run this app.</noscript><div id="root"><div class="loading-container"><div class="loading-spinner"></div><h2>Loading App Builder 201...</h2><p>Preparing your development environment</p></div></div><script>if ('serviceWorker' in navigator) {
      window.addEventListener('load', () => {
        navigator.serviceWorker.register('/service-worker.js')
          .then(registration => {
            console.log('Service Worker registered with scope:', registration.scope);

            // Check for updates
            registration.addEventListener('updatefound', () => {
              const newWorker = registration.installing;
              console.log('Service Worker update found!');

              newWorker.addEventListener('statechange', () => {
                if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
                  // New content is available, show notification
                  if (window.confirm('New version available! Reload to update?')) {
                    window.location.reload();
                  }
                }
              });
            });
          })
          .catch(error => {
            console.error('Service Worker registration failed:', error);
          });

        // Handle service worker updates
        let refreshing = false;
        navigator.serviceWorker.addEventListener('controllerchange', () => {
          if (!refreshing) {
            refreshing = true;
            window.location.reload();
          }
        });
      });
    }</script><script>// Load test script in development
    if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
      console.log('🧪 Loading App Builder test script...');

      // Simple test function that can be run in console
      window.testAppBuilderBasic = function () {
        console.log('🧪 Testing App Builder functionality...');

        const results = {
          reactLoaded: typeof window.React !== 'undefined',
          appLoaded: window.__APP_LOADED__ || false,
          hasComponents: document.body.textContent.includes('Components'),
          hasCanvas: document.body.textContent.includes('Canvas') || document.body.textContent.includes('Preview'),
          hasButtons: document.querySelectorAll('button').length > 0
        };

        console.log('📊 Test Results:');
        console.log('React Loaded:', results.reactLoaded ? '✅' : '❌');
        console.log('App Loaded:', results.appLoaded ? '✅' : '❌');
        console.log('Has Components Section:', results.hasComponents ? '✅' : '❌');
        console.log('Has Canvas/Preview Area:', results.hasCanvas ? '✅' : '❌');
        console.log('Has Interactive Buttons:', results.hasButtons ? '✅' : '❌');

        const success = Object.values(results).every(Boolean);
        console.log('🎯 Overall Status:', success ? '✅ PASS' : '❌ FAIL');

        return results;
      };

      // Auto-run test after a delay
      setTimeout(() => {
        if (typeof window.testAppBuilderBasic === 'function') {
          window.testAppBuilderBasic();
        }
      }, 3000);
    }</script></body></html>